"use client"

import { useEffect } from "react"
import { initializeEnhancedScrolling, destroyEnhancedScrolling } from "@/lib/smooth-scroll"

interface ScrollProviderProps {
  children: React.ReactNode
}

export function ScrollProvider({ children }: ScrollProviderProps) {
  useEffect(() => {
    // Initialize enhanced scrolling behavior
    initializeEnhancedScrolling()

    // Cleanup on unmount
    return () => {
      destroyEnhancedScrolling()
    }
  }, [])

  return <>{children}</>
}
