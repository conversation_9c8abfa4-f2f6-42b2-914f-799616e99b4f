/**
 * Optimized smooth scrolling utility with 60FPS performance
 * Uses requestAnimationFrame for smooth animations and easing functions
 */

interface ScrollOptions {
  duration?: number
  easing?: (t: number) => number
  offset?: number
  callback?: () => void
}

// Easing functions for smooth animations
const easingFunctions = {
  easeInOutCubic: (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  },
  easeOutQuart: (t: number): number => {
    return 1 - (--t) * t * t * t
  },
  easeInOutQuart: (t: number): number => {
    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t
  }
}

class SmoothScroller {
  private animationFrame: number | null = null
  private isScrolling = false

  /**
   * Smooth scroll to a target element or position
   */
  public scrollTo(target: string | number | HTMLElement, options: ScrollOptions = {}): Promise<void> {
    return new Promise((resolve) => {
      // Cancel any existing scroll animation
      this.cancelScroll()

      const {
        duration = 800,
        easing = easingFunctions.easeInOutCubic,
        offset = -80, // Account for navbar height
        callback
      } = options

      let targetPosition: number

      if (typeof target === 'string') {
        const element = document.getElementById(target)
        if (!element) {
          console.warn(`Element with id "${target}" not found`)
          resolve()
          return
        }
        targetPosition = element.offsetTop + offset
      } else if (typeof target === 'number') {
        targetPosition = target
      } else if (target instanceof HTMLElement) {
        targetPosition = target.offsetTop + offset
      } else {
        console.warn('Invalid scroll target')
        resolve()
        return
      }

      const startPosition = window.pageYOffset
      const distance = targetPosition - startPosition
      const startTime = performance.now()

      // If distance is very small, just jump to position
      if (Math.abs(distance) < 5) {
        window.scrollTo(0, targetPosition)
        callback?.()
        resolve()
        return
      }

      this.isScrolling = true

      const animateScroll = (currentTime: number) => {
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = easing(progress)
        
        const currentPosition = startPosition + (distance * easedProgress)
        
        // Use scrollTo for better performance than setting scrollTop
        window.scrollTo(0, currentPosition)

        if (progress < 1 && this.isScrolling) {
          this.animationFrame = requestAnimationFrame(animateScroll)
        } else {
          this.isScrolling = false
          this.animationFrame = null
          callback?.()
          resolve()
        }
      }

      this.animationFrame = requestAnimationFrame(animateScroll)
    })
  }

  /**
   * Smooth scroll to top of page
   */
  public scrollToTop(options: ScrollOptions = {}): Promise<void> {
    return this.scrollTo(0, { ...options, offset: 0 })
  }

  /**
   * Cancel current scroll animation
   */
  public cancelScroll(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
    this.isScrolling = false
  }

  /**
   * Check if currently scrolling
   */
  public get isActive(): boolean {
    return this.isScrolling
  }
}

// Create singleton instance
export const smoothScroller = new SmoothScroller()

/**
 * Enhanced scroll behavior with momentum and better performance
 */
export class EnhancedScrollBehavior {
  private lastScrollTime = 0
  private scrollVelocity = 0
  private momentumFrame: number | null = null
  private isUserScrolling = false
  private scrollTimeout: NodeJS.Timeout | null = null

  constructor() {
    this.init()
  }

  private init(): void {
    // Add passive event listeners for better performance
    window.addEventListener('wheel', this.handleWheel.bind(this), { passive: true })
    window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true })
    window.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true })
    window.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true })
    window.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true })
  }

  private handleWheel(event: WheelEvent): void {
    this.isUserScrolling = true
    this.scrollVelocity = event.deltaY
    this.lastScrollTime = performance.now()
    
    this.clearScrollTimeout()
    this.scrollTimeout = setTimeout(() => {
      this.isUserScrolling = false
      this.startMomentum()
    }, 150)
  }

  private handleScroll(): void {
    if (!this.isUserScrolling) return
    
    const now = performance.now()
    const deltaTime = now - this.lastScrollTime
    
    if (deltaTime > 0) {
      // Calculate scroll velocity for momentum
      const currentScroll = window.pageYOffset
      this.scrollVelocity = (currentScroll - (this.lastScrollTime || currentScroll)) / deltaTime
    }
    
    this.lastScrollTime = now
  }

  private handleTouchStart(): void {
    this.isUserScrolling = true
    this.cancelMomentum()
  }

  private handleTouchMove(): void {
    this.isUserScrolling = true
  }

  private handleTouchEnd(): void {
    this.clearScrollTimeout()
    this.scrollTimeout = setTimeout(() => {
      this.isUserScrolling = false
      this.startMomentum()
    }, 100)
  }

  private startMomentum(): void {
    if (Math.abs(this.scrollVelocity) < 0.1) return

    const friction = 0.95
    const minVelocity = 0.1

    const animateMomentum = () => {
      this.scrollVelocity *= friction
      
      if (Math.abs(this.scrollVelocity) > minVelocity && !this.isUserScrolling) {
        window.scrollBy(0, this.scrollVelocity * 16) // 16ms frame time
        this.momentumFrame = requestAnimationFrame(animateMomentum)
      } else {
        this.cancelMomentum()
      }
    }

    this.momentumFrame = requestAnimationFrame(animateMomentum)
  }

  private cancelMomentum(): void {
    if (this.momentumFrame) {
      cancelAnimationFrame(this.momentumFrame)
      this.momentumFrame = null
    }
  }

  private clearScrollTimeout(): void {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout)
      this.scrollTimeout = null
    }
  }

  public destroy(): void {
    this.cancelMomentum()
    this.clearScrollTimeout()
    window.removeEventListener('wheel', this.handleWheel.bind(this))
    window.removeEventListener('scroll', this.handleScroll.bind(this))
    window.removeEventListener('touchstart', this.handleTouchStart.bind(this))
    window.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    window.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }
}

// Initialize enhanced scroll behavior
let enhancedScrollBehavior: EnhancedScrollBehavior | null = null

export const initializeEnhancedScrolling = (): void => {
  if (typeof window !== 'undefined' && !enhancedScrollBehavior) {
    enhancedScrollBehavior = new EnhancedScrollBehavior()
  }
}

export const destroyEnhancedScrolling = (): void => {
  if (enhancedScrollBehavior) {
    enhancedScrollBehavior.destroy()
    enhancedScrollBehavior = null
  }
}

// Utility function for easy use in components
export const scrollToElement = (elementId: string, options?: ScrollOptions): Promise<void> => {
  return smoothScroller.scrollTo(elementId, options)
}
