"use client"

import { useEffect } from "react"
import { performanceMonitor, PerformanceOptimizer } from "@/lib/performance-monitor"

interface PerformanceProviderProps {
  children: React.ReactNode
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  useEffect(() => {
    // Start performance monitoring in development
    if (process.env.NODE_ENV === 'development') {
      performanceMonitor.start()
      console.log('🚀 Performance monitoring started')
      
      // Log device performance tier
      const tier = PerformanceOptimizer.getPerformanceTier()
      console.log(`📱 Device performance tier: ${tier}`)
      
      // Log hardware acceleration support
      const hasHardwareAccel = PerformanceOptimizer.supportsHardwareAcceleration()
      console.log(`🎮 Hardware acceleration: ${hasHardwareAccel ? 'Supported' : 'Not supported'}`)
    }

    // Optimize common elements for animations
    const optimizeElements = () => {
      // Optimize navigation elements
      const navElements = document.querySelectorAll('nav, header')
      navElements.forEach(el => {
        if (el instanceof HTMLElement) {
          PerformanceOptimizer.optimizeForAnimation(el, ['transform', 'opacity'])
        }
      })

      // Optimize cards and interactive elements
      const interactiveElements = document.querySelectorAll('[class*="card"], [class*="button"], [class*="hover"]')
      interactiveElements.forEach(el => {
        if (el instanceof HTMLElement) {
          PerformanceOptimizer.optimizeForAnimation(el, ['transform', 'box-shadow', 'border-color'])
        }
      })

      // Optimize background elements
      const backgroundElements = document.querySelectorAll('canvas, [class*="background"]')
      backgroundElements.forEach(el => {
        if (el instanceof HTMLElement) {
          PerformanceOptimizer.optimizeForAnimation(el, ['opacity', 'transform'])
        }
      })
    }

    // Initial optimization
    optimizeElements()

    // Re-optimize when new elements are added
    const observer = new MutationObserver(() => {
      // Debounce the optimization to avoid excessive calls
      PerformanceOptimizer.debounce(optimizeElements, 100)()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // Cleanup
    return () => {
      performanceMonitor.stop()
      observer.disconnect()
      
      // Remove optimizations from all elements
      const optimizedElements = document.querySelectorAll('[style*="will-change"]')
      optimizedElements.forEach(el => {
        if (el instanceof HTMLElement) {
          PerformanceOptimizer.removeAnimationOptimization(el)
        }
      })
    }
  }, [])

  return <>{children}</>
}
