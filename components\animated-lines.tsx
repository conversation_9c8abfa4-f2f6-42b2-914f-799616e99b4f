"use client"

import { useEffect, useRef } from 'react'

interface AnimatedLine {
  x: number
  y: number
  height: number
  speed: number
  opacity: number
  color: string
  width: number
  pulse: number
  pulseSpeed: number
}

interface AnimatedLinesProps {
  section: string
}

export default function AnimatedLines({ section }: AnimatedLinesProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameId = useRef<number>()
  const lines = useRef<AnimatedLine[]>([])
  const time = useRef(0)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Resize canvas
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      initLines()
    }

    // Create animated line
    const createLine = (): AnimatedLine => {
      const colors = [
        'rgba(255, 255, 255, 0.1)',
        'rgba(79, 70, 229, 0.15)',
        'rgba(124, 58, 237, 0.12)',
        'rgba(37, 99, 235, 0.1)',
        'rgba(56, 139, 253, 0.08)'
      ]

      return {
        x: Math.random() * canvas.width,
        y: -Math.random() * canvas.height,
        height: 100 + Math.random() * 300,
        speed: 0.5 + Math.random() * 1.5,
        opacity: 0.3 + Math.random() * 0.4,
        color: colors[Math.floor(Math.random() * colors.length)],
        width: 1 + Math.random() * 2,
        pulse: Math.random() * Math.PI * 2,
        pulseSpeed: 0.01 + Math.random() * 0.02
      }
    }

    // Initialize lines
    const initLines = () => {
      lines.current = []
      const lineCount = Math.floor((canvas.width * canvas.height) / 25000) // Responsive line count
      
      for (let i = 0; i < lineCount; i++) {
        lines.current.push(createLine())
      }
    }

    // Draw line
    const drawLine = (line: AnimatedLine) => {
      const pulseOpacity = line.opacity * (0.7 + Math.sin(time.current * line.pulseSpeed + line.pulse) * 0.3)
      
      ctx.save()
      ctx.globalAlpha = pulseOpacity
      
      // Create gradient for the line
      const gradient = ctx.createLinearGradient(line.x, line.y, line.x, line.y + line.height)
      gradient.addColorStop(0, 'transparent')
      gradient.addColorStop(0.1, line.color)
      gradient.addColorStop(0.5, line.color.replace(/[\d.]+\)/, '0.3)'))
      gradient.addColorStop(0.9, line.color)
      gradient.addColorStop(1, 'transparent')
      
      ctx.strokeStyle = gradient
      ctx.lineWidth = line.width
      ctx.lineCap = 'round'
      
      // Draw the line
      ctx.beginPath()
      ctx.moveTo(line.x, line.y)
      ctx.lineTo(line.x, line.y + line.height)
      ctx.stroke()
      
      // Add subtle glow effect
      ctx.shadowColor = line.color
      ctx.shadowBlur = 3
      ctx.stroke()
      
      ctx.restore()
    }

    // Animation loop
    const animate = (currentTime: number) => {
      time.current = currentTime
      
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // Update and draw lines
      lines.current.forEach(line => {
        // Move line down
        line.y += line.speed
        
        // Reset line when it goes off screen
        if (line.y > canvas.height + line.height) {
          line.y = -line.height
          line.x = Math.random() * canvas.width
        }
        
        drawLine(line)
      })
      
      animationFrameId.current = requestAnimationFrame(animate)
    }

    // Initialize
    resizeCanvas()
    animate(0)

    // Event listeners
    window.addEventListener('resize', resizeCanvas)

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas)
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [section])

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 z-1 pointer-events-none opacity-70"
      aria-hidden="true"
    />
  )
}
