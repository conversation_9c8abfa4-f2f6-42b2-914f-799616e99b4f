@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-rgb: 24, 119, 242;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-rgb: 56, 139, 253;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    cursor: none; /* Hide default cursor */
    /* Global performance optimizations */
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
  }

  html {
    scroll-behavior: smooth;
    /* Enable hardware acceleration for scrolling */
    transform: translateZ(0);
    overflow-x: hidden;
  }

  /* Global performance optimizations for all elements */
  * {
    box-sizing: border-box;
  }

  /* Optimize commonly animated elements */
  button, a, [role="button"] {
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
    transition-property: transform, opacity, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove will-change after hover/focus */
  button:not(:hover):not(:focus),
  a:not(:hover):not(:focus),
  [role="button"]:not(:hover):not(:focus) {
    will-change: auto;
  }

  /* Section transition animations with performance optimization */
  section {
    scroll-margin-top: 80px; /* Account for navbar height */
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform, opacity;
  }

  /* Optimize images and media */
  img, video, canvas {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Optimize SVG elements */
  svg {
    transform: translateZ(0);
    shape-rendering: optimizeSpeed;
  }

  /* High-performance container classes */
  .performance-container {
    transform: translateZ(0);
    backface-visibility: hidden;
    contain: layout style paint;
  }

  .performance-grid {
    display: grid;
    transform: translateZ(0);
    contain: layout;
  }

  .performance-flex {
    display: flex;
    transform: translateZ(0);
    contain: layout;
  }

  /* Enhanced cosmic cursor with nebula effect */
  .cosmic-cursor-optimized {
    background: radial-gradient(circle, rgba(255,255,255,0.98) 0%, rgba(56,139,253,0.9) 20%, rgba(124,58,237,0.8) 40%, rgba(79,70,229,0.6) 70%, transparent 100%);
    border-radius: 50%;
    mix-blend-mode: screen;
    filter: blur(0.8px);
    box-shadow:
      0 0 12px rgba(255,255,255,0.95),
      0 0 20px rgba(56,139,253,0.8),
      0 0 30px rgba(124,58,237,0.6),
      0 0 40px rgba(79,70,229,0.4),
      0 0 50px rgba(255,255,255,0.2);
    /* Removed animation to prevent transform conflicts */
  }

  .cosmic-cursor-optimized::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,0.9) 60%, transparent 100%);
    border-radius: 50%;
    box-shadow:
      0 0 6px rgba(255,255,255,1),
      0 0 10px rgba(56,139,253,0.9),
      0 0 15px rgba(124,58,237,0.6);
  }

  /* Enhanced trail particle styles with nebula effects */
  .cosmic-trail-particle {
    transition: opacity 0.2s ease-out, transform 0.1s ease-out;
    animation: nebula-drift 3s ease-in-out infinite;
  }

  @keyframes nebula-drift {
    0%, 100% {
      filter: blur(1px) hue-rotate(0deg);
    }
    33% {
      filter: blur(1.5px) hue-rotate(10deg);
    }
    66% {
      filter: blur(1px) hue-rotate(-10deg);
    }
  }
}

@layer components {
  .neo-card {
    @apply bg-black/20 backdrop-blur-lg border border-white/10 rounded-xl shadow-[0_0_15px_rgba(56,139,253,0.15)];
  }

  .neo-glow {
    @apply shadow-[0_0_15px_rgba(56,139,253,0.3)];
  }

  .neo-text {
    @apply text-white drop-shadow-[0_0_8px_rgba(56,139,253,0.8)];
  }

  .floating {
    animation: float 6s ease-in-out infinite;
  }

  .floating-delay-1 {
    animation: float 6s ease-in-out 1s infinite;
  }

  .floating-delay-2 {
    animation: float 6s ease-in-out 2s infinite;
  }

  .floating-delay-3 {
    animation: float 6s ease-in-out 3s infinite;
  }
  
  .animate-slow-spin {
    animation: slow-spin 20s linear infinite;
  }

  .animate-float-3d {
    animation: float-3d 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:scale-110:hover {
    transform: scale(1.1);
  }

  .hover\:-translate-y-1:hover {
    transform: translateY(-0.25rem);
  }

  .hover\:rotate-3:hover {
    transform: rotate(3deg);
  }

  .hover\:-rotate-3:hover {
    transform: rotate(-3deg);
  }

  .hover\:-rotate-12:hover {
    transform: rotate(-12deg);
  }

  .hover\:rotate-12:hover {
    transform: rotate(12deg);
  }

  .hover\:rotate-45:hover {
    transform: rotate(45deg);
  }

  .hover\:-rotate-45:hover {
    transform: rotate(-45deg);
  }

  /* Galaxy-specific styles */
  .galaxy-glow {
    @apply shadow-[0_0_30px_rgba(79,70,229,0.4),0_0_60px_rgba(124,58,237,0.2)];
  }

  .cosmic-text {
    @apply text-white drop-shadow-[0_0_12px_rgba(255,255,255,0.8)];
  }

  .nebula-card {
    @apply bg-black/30 backdrop-blur-xl border border-white/20 rounded-xl shadow-[0_0_25px_rgba(79,70,229,0.2)];
  }

  .star-twinkle {
    animation: star-twinkle 3s ease-in-out infinite;
  }

  .cosmic-drift {
    animation: cosmic-drift 20s linear infinite;
  }

  /* Smooth scroll animations */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .section-enter {
    animation: section-fade-in 0.8s ease-out forwards;
  }

  .section-enter-slow {
    animation: section-fade-in-slow 1.2s ease-out forwards;
  }

  /* Mobile navigation - optimized blur with hardware acceleration */
  .mobile-nav-open > * {
    filter: blur(8px) brightness(0.3);
    transition: filter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: filter;
    transform: translateZ(0); /* Force GPU acceleration */
  }

  /* Keep navbar and mobile menu unblurred */
  .mobile-nav-open header,
  .mobile-nav-open header * {
    filter: none !important;
    will-change: auto;
  }

  /* Performance optimization classes */
  .transform-gpu {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .optimize-animations {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .optimize-scroll {
    will-change: scroll-position;
    transform: translateZ(0);
  }

  .optimize-hover {
    will-change: transform, box-shadow, border-color;
    transition-property: transform, box-shadow, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Remove will-change after animations */
  .optimize-animations.animation-complete {
    will-change: auto;
  }

  /* High performance animation classes */
  .animate-smooth-scale {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }

  .animate-smooth-scale:hover {
    transform: scale(1.05) translateZ(0);
  }

  .animate-smooth-fade {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
  }

  /* Optimized blur effects */
  .backdrop-blur-optimized {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    will-change: backdrop-filter;
  }

  /* Performance-focused utility classes */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .smooth-60fps {
    will-change: transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
  }

  .optimize-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  .contain-strict {
    contain: strict;
  }

  /* Responsive performance optimizations */
  @media (max-width: 768px) {
    /* Reduce animations on mobile for better performance */
    .mobile-reduce-motion {
      animation-duration: 0.5s !important;
      transition-duration: 0.2s !important;
    }

    /* Optimize mobile scrolling */
    body {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: none;
    }
  }

  /* High refresh rate display optimizations */
  @media (min-resolution: 120dpi) {
    .high-refresh-optimize {
      animation-timing-function: linear;
      transition-timing-function: linear;
    }
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes slow-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float-3d {
  0% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
  33% {
    transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  }
  66% {
    transform: translateY(-5px) rotateX(-5deg) rotateY(-5deg);
  }
  100% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(56, 139, 253, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(56, 139, 253, 0.6);
  }
}

@keyframes star-twinkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes cosmic-drift {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(10px) translateY(-5px) rotate(90deg);
  }
  50% {
    transform: translateX(0) translateY(-10px) rotate(180deg);
  }
  75% {
    transform: translateX(-10px) translateY(-5px) rotate(270deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(360deg);
  }
}

@keyframes cosmic-cursor-pulse {
  0%, 100% {
    transform: scale(1) translateZ(0);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) translateZ(0);
    opacity: 1;
  }
}

/* Optimized keyframes with GPU acceleration */
@keyframes optimized-float {
  0% {
    transform: translateY(0px) translateZ(0);
  }
  50% {
    transform: translateY(-10px) translateZ(0);
  }
  100% {
    transform: translateY(0px) translateZ(0);
  }
}

@keyframes optimized-spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

@keyframes optimized-pulse {
  0%, 100% {
    transform: scale(1) translateZ(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.05) translateZ(0);
    opacity: 0.8;
  }
}

.font-sans {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
}

.font-poppins {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
}

.font-space-grotesk {
  font-family: var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
}

@keyframes section-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes section-fade-in-slow {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

