"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { FileText, ChevronRight, User, Code, FolderOpen, Briefcase, Mail, Menu, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { scrollToElement } from "@/lib/smooth-scroll"

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState("home")
  const [showNav, setShowNav] = useState(false)
  const [clickedItem, setClickedItem] = useState<string | null>(null)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100

      setShowNav(true)
      setScrolled(window.scrollY > 10)

      const sections = document.querySelectorAll("section[id]")

      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop
        const sectionHeight = (section as HTMLElement).offsetHeight
        const sectionId = section.getAttribute("id") || ""

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          setActiveSection(sectionId)
        }
      })
    }

    setShowNav(true)
    handleScroll()

    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
      // Cleanup: remove blur class when component unmounts
      document.body.classList.remove('mobile-nav-open')
    }
  }, [])

  const toggleMenu = () => {
    setIsOpen(!isOpen)
    // Add/remove blur class to body when mobile menu opens/closes with optimized transitions
    if (!isOpen) {
      document.body.classList.add('mobile-nav-open')
      // Force hardware acceleration for smooth blur transition
      document.body.style.willChange = 'filter'
    } else {
      document.body.classList.remove('mobile-nav-open')
      // Remove will-change after transition
      setTimeout(() => {
        document.body.style.willChange = 'auto'
      }, 300)
    }
  }

  const smoothScrollTo = async (targetId: string) => {
    // Close mobile menu if open
    setIsOpen(false)
    // Remove blur when navigation item is clicked
    document.body.classList.remove('mobile-nav-open')
    document.body.style.willChange = 'auto'

    // Add click animation feedback
    setClickedItem(targetId)
    setTimeout(() => setClickedItem(null), 300)

    // Use optimized smooth scrolling
    try {
      await scrollToElement(targetId, {
        duration: 800,
        offset: -80, // Account for navbar height
        callback: () => {
          // Optional callback after scroll completes
        }
      })
    } catch (error) {
      console.warn('Smooth scroll failed:', error)
      // Fallback to native scroll
      const element = document.getElementById(targetId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
  }

  const navLinks = [
    { name: "About", href: "#about", icon: User },
    { name: "Skills", href: "#skills", icon: Code },
    { name: "Projects", href: "#projects", icon: FolderOpen },
    { name: "Experience", href: "#experience", icon: Briefcase },
    { name: "Contact", href: "#contact", icon: Mail },
  ]

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-500 flex justify-center",
        showNav ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-full",
        scrolled ? "pt-4" : "pt-6"
      )}
    >
      {/* Desktop Navigation */}
      <nav className={cn(
        "hidden md:flex items-center px-6 py-3 rounded-full transition-all duration-500",
        "backdrop-blur-2xl border shadow-lg",
        scrolled
          ? "bg-black/40 border-white/30 shadow-black/30"
          : "bg-black/20 border-white/15 shadow-black/20",
        "before:absolute before:inset-0 before:rounded-full before:bg-gradient-to-r before:from-white/5 before:via-transparent before:to-white/5 before:-z-10",
        "after:absolute after:inset-0 after:rounded-full after:bg-gradient-to-b after:from-transparent after:via-black/10 after:to-black/20 after:-z-10"
      )}>
        <div className="flex items-center space-x-1">
          {navLinks.map((link) => (
            <button
              key={link.name}
              onClick={() => smoothScrollTo(link.href.substring(1))}
              className={cn(
                "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 relative group",
                activeSection === link.href.substring(1)
                  ? "text-white bg-white/10 shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/5",
                clickedItem === link.href.substring(1) && "scale-95 bg-primary/20"
              )}
            >
              {activeSection === link.href.substring(1) && (
                <motion.span
                  layoutId="navbar-active-pill"
                  className="absolute inset-0 bg-white/10 rounded-full -z-10 shadow-lg"
                  transition={{ type: "spring", duration: 0.6 }}
                />
              )}
              {link.name}
            </button>
          ))}

          <div className="w-px h-6 bg-white/20 mx-2" />

          <Button
            size="sm"
            className="bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-full px-4 py-2 shadow-lg"
            onClick={() => window.open("https://drive.google.com/file/d/1cB98YnieMnp488ptjxFEjATda_TXgj8t/view?usp=sharing", "_blank")}
          >
            <FileText size={14} className="mr-1" />
            Resume
            <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
          </Button>
        </div>
      </nav>

      {/* Mobile Hamburger Menu Button */}
      <button
        className="md:hidden fixed top-4 right-4 z-[10000] text-white backdrop-blur-2xl p-3 rounded-full border transition-all duration-300 shadow-lg bg-black/40 border-white/20 hover:border-primary/40 hover:bg-black/50"
        onClick={toggleMenu}
        aria-label="Toggle menu"
      >
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Mobile Navigation Menu */}
      <AnimatePresence mode="wait">
        {isOpen && (
          <motion.div
              initial={{ opacity: 0, scale: 0.9, y: -30, rotateX: -15 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                rotateX: 0,
                transition: {
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                  mass: 0.8
                }
              }}
              exit={{
                opacity: 0,
                scale: 0.9,
                y: -30,
                rotateX: -15,
                transition: {
                  duration: 0.2,
                  ease: "easeInOut"
                }
              }}
              className="md:hidden fixed top-20 right-4 z-[9999] backdrop-blur-2xl border rounded-2xl shadow-2xl overflow-hidden transform-gpu bg-black/70 border-white/30 shadow-black/40"
              style={{ willChange: 'transform, opacity' }}
            >
              <div className="p-6 flex flex-col space-y-4 min-w-[200px]">
                {navLinks.map((link) => {
                  const IconComponent = link.icon
                  return (
                    <motion.button
                      key={link.name}
                      onClick={() => smoothScrollTo(link.href.substring(1))}
                      className={cn(
                        "flex items-center space-x-3 py-3 px-4 rounded-full text-left hover:bg-white/10 transition-all duration-300 w-full transform-gpu",
                        activeSection === link.href.substring(1)
                          ? "text-white bg-white/10 shadow-lg"
                          : "text-white/80 hover:text-white",
                        clickedItem === link.href.substring(1) && "scale-95 bg-primary/20"
                      )}
                      whileHover={{
                        scale: 1.02,
                        x: 4,
                        transition: { duration: 0.2 }
                      }}
                      whileTap={{
                        scale: 0.98,
                        transition: { duration: 0.1 }
                      }}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        transition: {
                          delay: 0.1 + (navLinks.findIndex(l => l.name === link.name) * 0.05),
                          duration: 0.3
                        }
                      }}
                    >
                      <IconComponent size={20} />
                      <span className="font-medium">{link.name}</span>
                    </motion.button>
                  )
                })}

                {/* Separator */}
                <div className="w-full h-px bg-white/20 my-2" />

                <Button
                  className="w-full bg-gradient-to-r from-primary/80 to-blue-500/80 hover:from-primary hover:to-blue-500 text-white group transition-all duration-300 border-none rounded-full shadow-lg"
                  size="sm"
                  onClick={() => {
                    window.open("https://drive.google.com/file/d/1cB98YnieMnp488ptjxFEjATda_TXgj8t/view?usp=sharing", "_blank")
                    setIsOpen(false)
                    document.body.classList.remove('mobile-nav-open')
                  }}
                >
                  <FileText size={14} className="mr-2" />
                  Resume
                  <ChevronRight size={14} className="ml-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
                </Button>
              </div>
            </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
