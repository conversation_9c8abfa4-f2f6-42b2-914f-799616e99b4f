"use client"

import { useEffect, useRef, useCallback } from "react"

interface TrailPoint {
  x: number
  y: number
  opacity: number
  scale: number
  id: number
  element: HTMLDivElement
}

export function CosmicCursor() {
  const cursorRef = useRef<HTMLDivElement>(null)
  const trailRef = useRef<TrailPoint[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })
  const isVisibleRef = useRef(false)
  const animationFrameRef = useRef<number>()
  const lastTimeRef = useRef(0)
  const idCounterRef = useRef(0)

  // Create trail element
  const createTrailElement = useCallback((x: number, y: number): HTMLDivElement => {
    const element = document.createElement('div')
    element.className = 'cosmic-trail-particle'
    element.style.cssText = `
      position: fixed;
      pointer-events: none;
      z-index: 9998;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      will-change: transform, opacity;
      transform: translate3d(${x - 3}px, ${y - 3}px, 0) scale(0.3);
      opacity: 0;
      background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(56,139,253,0.6) 50%, transparent 100%);
      box-shadow: 0 0 4px rgba(255,255,255,0.5);
      filter: blur(0.5px);
      mix-blend-mode: screen;
    `
    document.body.appendChild(element)
    return element
  }, [])

  // Optimized animation loop using requestAnimationFrame
  const animate = useCallback((currentTime: number) => {
    const deltaTime = currentTime - lastTimeRef.current

    if (deltaTime >= 16.67) { // ~60fps
      // Update cursor position
      if (cursorRef.current && isVisibleRef.current) {
        cursorRef.current.style.transform = `translate3d(${mouseRef.current.x - 10}px, ${mouseRef.current.y - 10}px, 0)`
      }

      // Update trail particles
      trailRef.current = trailRef.current.filter(point => {
        point.opacity -= 0.08 // Faster fade for smoother trail
        point.scale = Math.min(1, point.scale + 0.1)

        if (point.opacity <= 0) {
          point.element.remove()
          return false
        }

        // Use transform3d for GPU acceleration
        point.element.style.transform = `translate3d(${point.x - 3}px, ${point.y - 3}px, 0) scale(${point.scale})`
        point.element.style.opacity = point.opacity.toString()

        return true
      })

      lastTimeRef.current = currentTime
    }

    animationFrameRef.current = requestAnimationFrame(animate)
  }, [])

  // Optimized mouse move handler
  const handleMouseMove = useCallback((e: MouseEvent) => {
    mouseRef.current = { x: e.clientX, y: e.clientY }
    isVisibleRef.current = true

    // Add new trail point (throttled)
    if (trailRef.current.length === 0 ||
        Math.abs(e.clientX - trailRef.current[trailRef.current.length - 1]?.x) > 5 ||
        Math.abs(e.clientY - trailRef.current[trailRef.current.length - 1]?.y) > 5) {

      const element = createTrailElement(e.clientX, e.clientY)
      const newPoint: TrailPoint = {
        x: e.clientX,
        y: e.clientY,
        opacity: 1,
        scale: 0.3,
        id: idCounterRef.current++,
        element
      }

      trailRef.current.push(newPoint)

      // Keep only last 6 trail points for better performance
      if (trailRef.current.length > 6) {
        const removed = trailRef.current.shift()
        removed?.element.remove()
      }
    }
  }, [createTrailElement])

  const handleMouseLeave = useCallback(() => {
    isVisibleRef.current = false
    if (cursorRef.current) {
      cursorRef.current.style.opacity = '0'
    }
  }, [])

  const handleMouseEnter = useCallback(() => {
    isVisibleRef.current = true
    if (cursorRef.current) {
      cursorRef.current.style.opacity = '1'
    }
  }, [])

  useEffect(() => {
    // Start animation loop
    animationFrameRef.current = requestAnimationFrame(animate)

    // Add event listeners with passive option for better performance
    document.addEventListener("mousemove", handleMouseMove, { passive: true })
    document.addEventListener("mouseleave", handleMouseLeave, { passive: true })
    document.addEventListener("mouseenter", handleMouseEnter, { passive: true })

    return () => {
      // Cleanup
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseleave", handleMouseLeave)
      document.removeEventListener("mouseenter", handleMouseEnter)

      // Clean up trail elements
      trailRef.current.forEach(point => point.element.remove())
      trailRef.current = []
    }
  }, [animate, handleMouseMove, handleMouseLeave, handleMouseEnter])

  return (
    <div
      ref={cursorRef}
      className="cosmic-cursor-optimized"
      style={{
        position: 'fixed',
        width: '20px',
        height: '20px',
        pointerEvents: 'none',
        zIndex: 9999,
        willChange: 'transform',
        opacity: 0,
        transition: 'opacity 0.2s ease-out'
      }}
    />
  )
}
