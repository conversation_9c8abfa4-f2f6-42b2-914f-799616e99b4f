"use client"

import { useEffect, useRef, useCallback } from "react"

interface TrailPoint {
  x: number
  y: number
  opacity: number
  scale: number
  id: number
  element: HTMLDivElement
}

export function CosmicCursor() {
  const cursorRef = useRef<HTMLDivElement>(null)
  const trailRef = useRef<TrailPoint[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })
  const isVisibleRef = useRef(false)
  const animationFrameRef = useRef<number>()
  const lastTimeRef = useRef(0)
  const idCounterRef = useRef(0)

  // Create trail element with enhanced nebula effect
  const createTrailElement = useCallback((x: number, y: number, size: number = 8): HTMLDivElement => {
    const element = document.createElement('div')
    element.className = 'cosmic-trail-particle'

    // Create multiple layers for nebula effect
    const colors = [
      'rgba(255,255,255,0.9)',
      'rgba(56,139,253,0.7)',
      'rgba(124,58,237,0.5)',
      'rgba(79,70,229,0.3)'
    ]

    element.style.cssText = `
      position: fixed;
      pointer-events: none;
      z-index: 9998;
      width: ${size}px;
      height: ${size}px;
      border-radius: 50%;
      will-change: transform, opacity;
      transform: translate3d(${x - size/2}px, ${y - size/2}px, 0) scale(0.8);
      opacity: 0.9;
      background: radial-gradient(circle, ${colors[0]} 0%, ${colors[1]} 30%, ${colors[2]} 60%, ${colors[3]} 80%, transparent 100%);
      box-shadow:
        0 0 ${size}px ${colors[1]},
        0 0 ${size * 1.5}px ${colors[2]},
        0 0 ${size * 2}px ${colors[3]};
      filter: blur(1px);
      mix-blend-mode: screen;
    `
    document.body.appendChild(element)
    return element
  }, [])

  // Optimized animation loop using requestAnimationFrame
  const animate = useCallback((currentTime: number) => {
    const deltaTime = currentTime - lastTimeRef.current

    if (deltaTime >= 16.67) { // ~60fps
      // Update cursor position
      if (cursorRef.current && isVisibleRef.current) {
        cursorRef.current.style.transform = `translate3d(${mouseRef.current.x - 10}px, ${mouseRef.current.y - 10}px, 0)`
      }

      // Update trail particles with enhanced nebula effects
      trailRef.current = trailRef.current.filter(point => {
        point.opacity -= 0.04 // Slower fade for longer-lasting nebula trail
        point.scale = Math.max(0.5, point.scale - 0.02) // Gradually shrink

        if (point.opacity <= 0) {
          point.element.remove()
          return false
        }

        // Add subtle drift for organic nebula movement
        const drift = Math.sin(currentTime * 0.001 + point.id) * 0.5
        const driftY = Math.cos(currentTime * 0.0008 + point.id) * 0.3

        // Use transform3d for GPU acceleration with drift
        point.element.style.transform = `translate3d(${point.x + drift - 6}px, ${point.y + driftY - 6}px, 0) scale(${point.scale})`
        point.element.style.opacity = point.opacity.toString()

        return true
      })

      lastTimeRef.current = currentTime
    }

    animationFrameRef.current = requestAnimationFrame(animate)
  }, [])

  // Optimized mouse move handler with enhanced trail creation
  const handleMouseMove = useCallback((e: MouseEvent) => {
    const newX = e.clientX
    const newY = e.clientY

    mouseRef.current = { x: newX, y: newY }
    isVisibleRef.current = true

    // Show cursor immediately
    if (cursorRef.current) {
      cursorRef.current.style.opacity = '1'
    }

    // Create trail points more frequently for smoother effect
    const lastPoint = trailRef.current[trailRef.current.length - 1]
    const shouldCreateTrail = trailRef.current.length === 0 ||
      !lastPoint ||
      Math.abs(newX - lastPoint.x) > 2 ||
      Math.abs(newY - lastPoint.y) > 2

    if (shouldCreateTrail) {
      // Create multiple trail particles with varying sizes for nebula effect
      const sizes = [10, 7, 5]
      const delays = [0, 25, 50]

      sizes.forEach((size, index) => {
        const createParticle = () => {
          const element = createTrailElement(
            newX + (Math.random() - 0.5) * 3,
            newY + (Math.random() - 0.5) * 3,
            size
          )

          const newPoint: TrailPoint = {
            x: newX,
            y: newY,
            opacity: 0.8 - (index * 0.15),
            scale: 0.9 + (index * 0.05),
            id: idCounterRef.current++,
            element
          }

          trailRef.current.push(newPoint)

          // Keep only last 10 trail points for optimal performance
          if (trailRef.current.length > 10) {
            const removed = trailRef.current.shift()
            removed?.element.remove()
          }
        }

        if (delays[index] === 0) {
          createParticle()
        } else {
          setTimeout(createParticle, delays[index])
        }
      })
    }
  }, [createTrailElement])

  const handleMouseLeave = useCallback(() => {
    isVisibleRef.current = false
    if (cursorRef.current) {
      cursorRef.current.style.opacity = '0'
    }
  }, [])

  const handleMouseEnter = useCallback(() => {
    isVisibleRef.current = true
    if (cursorRef.current) {
      cursorRef.current.style.opacity = '1'
    }
  }, [])

  useEffect(() => {
    // Initialize cursor position
    const initializeCursor = () => {
      if (cursorRef.current) {
        // Get initial mouse position if available
        const rect = document.body.getBoundingClientRect()
        const centerX = window.innerWidth / 2
        const centerY = window.innerHeight / 2

        mouseRef.current = { x: centerX, y: centerY }
        cursorRef.current.style.transform = `translate3d(${centerX - 10}px, ${centerY - 10}px, 0)`
        cursorRef.current.style.opacity = '0' // Start hidden until mouse moves
      }
    }

    initializeCursor()

    // Start animation loop
    animationFrameRef.current = requestAnimationFrame(animate)

    // Add event listeners with passive option for better performance
    document.addEventListener("mousemove", handleMouseMove, { passive: true })
    document.addEventListener("mouseleave", handleMouseLeave, { passive: true })
    document.addEventListener("mouseenter", handleMouseEnter, { passive: true })

    return () => {
      // Cleanup
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseleave", handleMouseLeave)
      document.removeEventListener("mouseenter", handleMouseEnter)

      // Clean up trail elements
      trailRef.current.forEach(point => point.element.remove())
      trailRef.current = []
    }
  }, [animate, handleMouseMove, handleMouseLeave, handleMouseEnter])

  return (
    <div
      ref={cursorRef}
      className="cosmic-cursor-optimized"
      style={{
        position: 'fixed',
        width: '20px',
        height: '20px',
        pointerEvents: 'none',
        zIndex: 9999,
        willChange: 'transform',
        opacity: 0,
        transition: 'opacity 0.2s ease-out'
      }}
    />
  )
}
