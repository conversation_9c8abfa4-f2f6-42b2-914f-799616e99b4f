/**
 * Performance monitoring utilities for 60FPS optimization
 */

interface PerformanceMetrics {
  fps: number
  frameTime: number
  memoryUsage?: number
  renderTime: number
}

interface PerformanceConfig {
  enableLogging?: boolean
  logInterval?: number
  targetFPS?: number
  enableMemoryMonitoring?: boolean
}

class PerformanceMonitor {
  private frames: number[] = []
  private lastTime = 0
  private frameCount = 0
  private config: PerformanceConfig
  private logTimer: NodeJS.Timeout | null = null
  private renderStartTime = 0

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableLogging: false,
      logInterval: 5000, // Log every 5 seconds
      targetFPS: 60,
      enableMemoryMonitoring: false,
      ...config
    }
  }

  /**
   * Start monitoring performance
   */
  public start(): void {
    if (this.config.enableLogging) {
      this.logTimer = setInterval(() => {
        this.logMetrics()
      }, this.config.logInterval)
    }
  }

  /**
   * Stop monitoring performance
   */
  public stop(): void {
    if (this.logTimer) {
      clearInterval(this.logTimer)
      this.logTimer = null
    }
  }

  /**
   * Mark the start of a frame render
   */
  public startFrame(): void {
    this.renderStartTime = performance.now()
  }

  /**
   * Mark the end of a frame render and calculate metrics
   */
  public endFrame(): PerformanceMetrics {
    const now = performance.now()
    const frameTime = now - this.lastTime
    const renderTime = now - this.renderStartTime

    // Track frame times for FPS calculation
    this.frames.push(frameTime)
    if (this.frames.length > 60) {
      this.frames.shift() // Keep only last 60 frames
    }

    this.lastTime = now
    this.frameCount++

    // Calculate average FPS
    const avgFrameTime = this.frames.reduce((a, b) => a + b, 0) / this.frames.length
    const fps = 1000 / avgFrameTime

    const metrics: PerformanceMetrics = {
      fps: Math.round(fps * 100) / 100,
      frameTime: Math.round(frameTime * 100) / 100,
      renderTime: Math.round(renderTime * 100) / 100
    }

    // Add memory usage if available and enabled
    if (this.config.enableMemoryMonitoring && 'memory' in performance) {
      const memory = (performance as any).memory
      metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100 // MB
    }

    return metrics
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetrics | null {
    if (this.frames.length === 0) return null

    const avgFrameTime = this.frames.reduce((a, b) => a + b, 0) / this.frames.length
    const fps = 1000 / avgFrameTime

    const metrics: PerformanceMetrics = {
      fps: Math.round(fps * 100) / 100,
      frameTime: Math.round(avgFrameTime * 100) / 100,
      renderTime: 0 // Not available without active frame
    }

    if (this.config.enableMemoryMonitoring && 'memory' in performance) {
      const memory = (performance as any).memory
      metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100
    }

    return metrics
  }

  /**
   * Check if performance is below target
   */
  public isPerformancePoor(): boolean {
    const metrics = this.getMetrics()
    if (!metrics) return false
    
    return metrics.fps < (this.config.targetFPS! * 0.8) // 80% of target FPS
  }

  /**
   * Log performance metrics to console
   */
  private logMetrics(): void {
    const metrics = this.getMetrics()
    if (!metrics) return

    const status = metrics.fps >= this.config.targetFPS! * 0.9 ? '✅' : 
                   metrics.fps >= this.config.targetFPS! * 0.7 ? '⚠️' : '❌'

    console.group(`${status} Performance Metrics`)
    console.log(`FPS: ${metrics.fps} (Target: ${this.config.targetFPS})`)
    console.log(`Frame Time: ${metrics.frameTime}ms`)
    console.log(`Render Time: ${metrics.renderTime}ms`)
    
    if (metrics.memoryUsage) {
      console.log(`Memory Usage: ${metrics.memoryUsage}MB`)
    }
    
    if (metrics.fps < this.config.targetFPS! * 0.8) {
      console.warn('Performance is below target. Consider optimizations.')
    }
    
    console.groupEnd()
  }
}

/**
 * Global performance monitor instance
 */
export const performanceMonitor = new PerformanceMonitor({
  enableLogging: process.env.NODE_ENV === 'development',
  enableMemoryMonitoring: process.env.NODE_ENV === 'development'
})

/**
 * Performance optimization utilities
 */
export class PerformanceOptimizer {
  private static rafCallbacks: Set<() => void> = new Set()
  private static rafId: number | null = null

  /**
   * Batch multiple RAF callbacks into a single frame
   */
  public static batchRAF(callback: () => void): void {
    this.rafCallbacks.add(callback)
    
    if (!this.rafId) {
      this.rafId = requestAnimationFrame(() => {
        this.rafCallbacks.forEach(cb => cb())
        this.rafCallbacks.clear()
        this.rafId = null
      })
    }
  }

  /**
   * Debounce function for performance-critical operations
   */
  public static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate = false
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null
    
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }
      
      const callNow = immediate && !timeout
      
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      
      if (callNow) func(...args)
    }
  }

  /**
   * Throttle function for high-frequency events
   */
  public static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  /**
   * Optimize element for animations by adding will-change
   */
  public static optimizeForAnimation(element: HTMLElement, properties: string[] = ['transform']): void {
    element.style.willChange = properties.join(', ')
    element.style.transform = 'translateZ(0)' // Force hardware acceleration
  }

  /**
   * Remove animation optimizations
   */
  public static removeAnimationOptimization(element: HTMLElement): void {
    element.style.willChange = 'auto'
  }

  /**
   * Check if device supports hardware acceleration
   */
  public static supportsHardwareAcceleration(): boolean {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    return !!gl
  }

  /**
   * Get device performance tier (rough estimation)
   */
  public static getPerformanceTier(): 'low' | 'medium' | 'high' {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl')
    
    if (!gl) return 'low'
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    if (!debugInfo) return 'medium'
    
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
    
    // Simple heuristic based on GPU names
    if (renderer.includes('Intel') && !renderer.includes('Iris')) return 'low'
    if (renderer.includes('Mali') || renderer.includes('Adreno')) return 'medium'
    if (renderer.includes('GeForce') || renderer.includes('Radeon')) return 'high'
    
    return 'medium'
  }
}

/**
 * Hook for React components to monitor performance
 */
export const usePerformanceMonitor = () => {
  const startFrame = () => performanceMonitor.startFrame()
  const endFrame = () => performanceMonitor.endFrame()
  const getMetrics = () => performanceMonitor.getMetrics()
  const isPerformancePoor = () => performanceMonitor.isPerformancePoor()

  return {
    startFrame,
    endFrame,
    getMetrics,
    isPerformancePoor
  }
}
